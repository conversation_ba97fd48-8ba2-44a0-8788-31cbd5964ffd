package com.smsautoforward.smsautoforwardapp


import android.content.*
import android.os.Build
import android.os.Bundle
import android.telephony.SmsMessage
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONException
import org.json.JSONObject
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*


class SmsBackgroundReceiver : BroadcastReceiver() {




    override fun onReceive(context: Context?, intent: Intent?) {

        try {
            Log.d(TAG, "onReceive is running")
            if (intent?.action == "android.provider.Telephony.SMS_RECEIVED") {

                val bundle: Bundle? = intent.extras
                val messages: Array<SmsMessage?>
                val format = bundle?.getString("format")

                // Retrieve SMS messages
                if (bundle != null) {

                    val pdus = bundle["pdus"] as Array<*>?
                    if (pdus != null) {
                        val isVersionM =
                            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
                        messages = arrayOfNulls(pdus.size)
                        for (i in messages.indices) {
                            // For version M and above, use format based on format value.
                            if (isVersionM) {
                                messages[i] =
                                    SmsMessage.createFromPdu(pdus[i] as ByteArray, format)
                            } else {
                                messages[i] =
                                    SmsMessage.createFromPdu(pdus[i] as ByteArray)
                            }

                            // Process the SMS message
                            val smsMessage: SmsMessage = messages[i]!!

                            fetchForwardAllData(context, smsMessage)
                            fetchRules(context, smsMessage)

                        }
                    }
                } else {
                    Log.d(TAG, "bundle is null")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in SMS background receiver: ${e.message}")
        }
    }

    private fun fetchRules(context: Context?, smsMessage: SmsMessage) {

        // Get authenticated Firestore instance and user ID
        val authHelper = FirebaseAuthHelper.getInstance()
        val firestore = authHelper.getAuthenticatedFirestore(context!!)
        val uid = authHelper.getCurrentUserId(context)

        if (uid != null && firestore != null) {
            firestore
                    .collection("users")
                    .document(uid)
                    .get()
                    .addOnCompleteListener { task: Task<DocumentSnapshot> ->
                        if (task.isSuccessful) {

                            val document: DocumentSnapshot? = task.result
                            if (document != null && document.exists()) {
                                // Document exists, parse the data into your ForwardAll model
                                val data: Map<String, Any>? = document.data
                                if (data != null) {
                                    val noOfForwardsused: Int  = (data["noOfForwardsused"] as? Long)?.toInt() ?: 0
                                    val noOfForwardsPerMonth: Int  = (data["noOfForwardsPerMonth"] as? Long)?.toInt() ?: 0





            firestore
                .collection("users")
                .document(uid)
                .collection("rules")
                .get()
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        for (document in task.result!!) {
                            // Handle each rule document here
                            val data = document.data

                          



                                // Extract values from Firestore document data
                                val ruleId = data["ruleID"] as? String
                                val isActive = data["isActive"] as? Boolean
                                val ruleName = data["ruleName"] as? String
                                val keywords = data["keywords"] as? List<String>
                                val fromNumber = data["fromNumber"] as? String
                                val isCaseSensitive = data["isCaseSensitive"] as? Boolean
                                val isEmail = data["isEmail"] as? Boolean
                                val recipients = data["recipients"] as? List<String>
                                val method = data["method"] as? String
                                val url = data["url"] as? String
                                val jsonBody = data["jsonBody"] as? String

                                // Create a JSON object to represent the rule
                                val ruleData = mapOf(
                                    "ruleID" to ruleId,
                                    "isActive" to isActive,
                                    "ruleName" to ruleName,
                                    "keywords" to keywords,
                                    "fromNumber" to fromNumber,
                                    "isCaseSensitive" to isCaseSensitive,
                                    "isEmail" to isEmail,
                                    "recipients" to recipients,
                                    "method" to method,
                                    "url" to url,
                                    "jsonBody" to jsonBody
                                )

                                // Convert the JSON object to a JSON string
                                val ruleJson = JSONObject(ruleData).toString()


                                // Check if the rule is active, isEmail is true, and sender matches fromNumber
                                if (isActive == true) {


                                    val containsKeyword = keywords?.any { keyword ->
                                        smsMessage.messageBody?.contains(
                                            keyword,
                                            ignoreCase = !isCaseSensitive!!
                                        ) == true
                                    } ?: false

                                    if (smsMessage.displayOriginatingAddress == fromNumber && containsKeyword) {


                                        if (isEmail == true) {
                                            Log.d(TAG, "ITS EMAIL")
                                            val logData = mapOf(
                                                "logTitle" to "To EMAIL",
                                                "additionalMessage" to "",
                                                "ruleName" to ruleName,
                                                "keywords" to keywords,
                                                "senderNumber" to smsMessage.displayOriginatingAddress,
                                                "listOfReceiversEmail" to recipients,
                                                "message" to smsMessage.messageBody,
                                                "status" to true,
                                                "isEmail" to true,
                                                "dateTime" to SimpleDateFormat(
                                                    "yyyy-MM-dd HH:mm:ss",
                                                    Locale.getDefault()
                                                ).format(Date()),
                                                "httpMethod" to "", // Not used for email
                                                "url" to "", // Not used for email
                                                "jsonBody" to "" // Not used for email
                                            )




                                            if (recipients != null) {
                                                
                                                if (noOfForwardsused >= noOfForwardsPerMonth) {
                                                    
                                                    recipients?.forEach { recipient ->
                                                        uploadLogToFirestore(
                                                            context,
                                                            logData as Map<String, Any>,
                                                            false,
                                                            "$recipient",
                                                            additionalMessage = "due to forward limit reached, please upgrade"
                                                        )
                                                    }
                                                }else {


                                                    sendEmailWithLogData(
                                                        context,
                                                        recipients,
                                                        "New message from ${smsMessage.displayOriginatingAddress}",
                                                        "${smsMessage.messageBody}",
                                                        logData as Map<String, Any>


                                                    )
                                                }
                                            }

                                        } else {
                                            Log.d(TAG, "ITS URL")
                                            val logData = mapOf(
                                                "logTitle" to "To URL","additionalMessage" to "",
                                                "ruleName" to ruleName,
                                                "keywords" to keywords,
                                                "senderNumber" to smsMessage.displayOriginatingAddress,
                                                "listOfReceiversEmail" to listOf<String>(), // Empty list
                                                "message" to smsMessage.messageBody,
                                                "status" to true,
                                                "isEmail" to false,
                                                "dateTime" to SimpleDateFormat(
                                                    "yyyy-MM-dd HH:mm:ss",
                                                    Locale.getDefault()
                                                ).format(Date()),
                                                "httpMethod" to method.orEmpty(),
                                                "url" to url.orEmpty(),
                                                "jsonBody" to jsonBody.orEmpty()
                                            )



                                            if (url != null && method != null) {
                                                if (noOfForwardsused >= noOfForwardsPerMonth) {
                                                    uploadLogToFirestore(
                                                        context,
                                                        logData as Map<String, Any>,
                                                        false,
                                                        "",
                                                        additionalMessage = "due to forward limit reached, please upgrade"
                                                    )

                                                }else {
                                                    CoroutineScope(Dispatchers.Default).launch {
                                                        forwardSmsToUrl(
                                                            context,
                                                            smsMessage,
                                                            url,
                                                            method,
                                                            jsonBody,
                                                            logData as Map<String, Any>
                                                        )
                                                    }
                                                }
                                            }

                                        }
                                    }
                                }


                            }

                    } else {
                        // Handle the error
                        val exception = task.exception
                        if (exception != null) {
                            Log.e(TAG, "Error fetching rules: ${exception.message}")
                        }
                    }
                }
        }
    }}}}
        } else {
            Log.e(TAG, "Cannot fetch rules: uid=$uid, firestore=${firestore != null}")
        }
    }

    private fun fetchForwardAllData(context: Context?, smsMessage: SmsMessage) {
        // Get authenticated Firestore instance and user ID
        val authHelper = FirebaseAuthHelper.getInstance()
        val firestore = authHelper.getAuthenticatedFirestore(context!!)
        val uid = authHelper.getCurrentUserId(context)

        try {
            if (uid != null && firestore != null) {

                firestore
                    .collection("users")
                    .document(uid)
                    .get()
                    .addOnCompleteListener { task: Task<DocumentSnapshot> ->
                        if (task.isSuccessful) {

                            val document: DocumentSnapshot? = task.result
                            if (document != null && document.exists()) {
                                // Document exists, parse the data into your ForwardAll model
                                val data: Map<String, Any>? = document.data
                                if (data != null) {
                                    val noOfForwardsused: Int  = (data["noOfForwardsused"] as? Long)?.toInt() ?: 0
                                    val noOfForwardsPerMonth: Int  = (data["noOfForwardsPerMonth"] as? Long)?.toInt() ?: 0





                                        val forwardAllEmail =
                                            data["forwardAllEmail"] as? Map<String, Any>
                                        val forwardAllUrl =
                                            data["forwardAllUrl"] as? Map<String, Any>
                                        if (forwardAllEmail != null) {
                                            val isActive = forwardAllEmail["isActive"] as? Boolean

                                            val recipients =
                                                forwardAllEmail["recipients"] as? List<String>


                                            // Check for null values before using them
                                            if (isActive == true) {


                                                val logData = mapOf(
                                                    "logTitle" to "To EMAIL","additionalMessage" to "",
                                                    "ruleName" to "ForwardAllToEmail",
                                                    "keywords" to listOf<String>(),
                                                    "senderNumber" to smsMessage.displayOriginatingAddress,
                                                    "listOfReceiversEmail" to recipients,
                                                    "message" to smsMessage.messageBody,
                                                    "status" to true,
                                                    "isEmail" to true,
                                                    "dateTime" to SimpleDateFormat(
                                                        "yyyy-MM-dd HH:mm:ss",
                                                        Locale.getDefault()
                                                    ).format(Date()),
                                                    "httpMethod" to "", // Not used for email
                                                    "url" to "", // Not used for email
                                                    "jsonBody" to "" // Not used for email
                                                )



                                                if (recipients != null) {
                                                    if (noOfForwardsused >= noOfForwardsPerMonth) {
                                                        recipients?.forEach { recipient ->
                                                            uploadLogToFirestore(
                                                                context,
                                                                logData as Map<String, Any>,
                                                                false,
                                                                "$recipient",
                                                                additionalMessage = "due to forward limit reached, please upgrade"
                                                            )
                                                        }
                                                    }else {
                                                        sendEmailWithLogData(
                                                            context,
                                                            recipients,
                                                            "New message from ${smsMessage.displayOriginatingAddress}",
                                                            "${smsMessage.messageBody}",
                                                            logData as Map<String, Any>


                                                        )
                                                    }
                                                }


                                            }

//
                                        } else {
                                            Log.e(TAG, "forwardAllEmail is null")
                                        }
                                        if (forwardAllUrl != null) {
                                            val isActive = forwardAllUrl["isActive"] as? Boolean

                                            val method = forwardAllUrl["method"] as? String
                                            val url = forwardAllUrl["url"] as? String
                                            val jsonBody = forwardAllUrl["jsonBody"] as? String

                                            // Check for null values before using them
                                            if (isActive == true) {

                                                if (url != null && method != null) {

                                                    val logData = mapOf(
                                                        "logTitle" to "To URL","additionalMessage" to "",
                                                        "ruleName" to "ForwardAllToUrl",
                                                        "keywords" to listOf<String>(),
                                                        "senderNumber" to smsMessage.displayOriginatingAddress,
                                                        "listOfReceiversEmail" to listOf<String>(), // Empty list
                                                        "message" to smsMessage.messageBody,
                                                        "status" to true,
                                                        "isEmail" to false,
                                                        "dateTime" to // Format the timestamp as a string
                                                                SimpleDateFormat(
                                                                    "yyyy-MM-dd HH:mm:ss",
                                                                    Locale.getDefault()
                                                                ).format(Date()),
                                                        "httpMethod" to method.orEmpty(),
                                                        "url" to url.orEmpty(),
                                                        "jsonBody" to jsonBody.orEmpty()
                                                    )
                                                    if (noOfForwardsused >= noOfForwardsPerMonth) {
                                                        uploadLogToFirestore(
                                                            context,
                                                            logData as Map<String, Any>,
                                                            false,
                                                            "",
                                                            additionalMessage = "due to forward limit reached, please upgrade"
                                                        )

                                                    }else {
                                                        CoroutineScope(Dispatchers.Default).launch {
                                                            forwardSmsToUrl(
                                                                context,
                                                                smsMessage,
                                                                url,
                                                                method,
                                                                jsonBody,
                                                                logData
                                                            )
                                                        }
                                                    }


                                                }
                                            }

//
                                        } else {
                                            Log.e(TAG, "forwardAllUrl is null")
                                        }


                                }
                            } else {
                                // Document doesn't exist
                                Log.d(TAG, "Document doesn't exist")
                            }
                        } else {
                            // Handle the error here
                            val exception = task.exception
                            if (exception != null) {
                                Log.e(TAG, "Error fetching ForwardAll data: " + exception.message)
                            }
                        }
                    }
            } else {
                Log.e(TAG, "Cannot fetch forward all data: uid=$uid, firestore=${firestore != null}")
            }
        } catch (e: Exception) {

            // Handle exceptions here
            Log.e(TAG, "Exception: " + e.message)
        }
    }
    fun registerReceiver(context: Context) {
        context.registerReceiver(this, IntentFilter("android.provider.Telephony.SMS_RECEIVED"))
    }

    private suspend fun forwardSmsToUrl(
        context: Context?,
        SmsMessagee: SmsMessage,
        urll: String,
        httpmethodd: String,
        jsonBody: String?,
        logData: Map<String, Any>
    ) {
        val url = urll // Replace with the target URL
        val httpMethod = httpmethodd // Specify the HTTP method (e.g., POST, GET, etc.)

        try {
            // Start a coroutine on the IO dispatcher for network operations
            withContext(Dispatchers.IO) {
                val urlConnection = URL(url).openConnection() as HttpURLConnection
                urlConnection.requestMethod = httpMethod
                urlConnection.setRequestProperty("Content-Type", "application/json")

                // If the HTTP method is not GET and a jsonBody is provided, add it to the request
                if (httpMethod != "GET" && jsonBody != null) {
                    // Create a JSON object with the SMS details
                    val smsData = JSONObject()

                    // Extract sender and body from SmsMessage
                    val sender = SmsMessagee.displayOriginatingAddress
                    val body = SmsMessagee.messageBody

                    // Parse the jsonBody and add its properties to the SMS data
                    try {
                        val additionalData = JSONObject(jsonBody)

                        // Iterate through the keys and values in additionalData
                        val keys = additionalData.keys()
                        while (keys.hasNext()) {
                            val key = keys.next() as String
                            var value = additionalData.get(key).toString()

                            // Replace placeholders in the value with actual values
                            value = value.replace("{sender}", sender)
                            value = value.replace("{body}", body)

                            // Add the modified value to smsData
                            smsData.put(key, value)
                        }
                    } catch (e: JSONException) {
                        Log.e(TAG, "Error parsing jsonBody: $e")
                    }

                    // Log the JSON data being sent
                    Log.d(TAG, "Forwarding SMS to URL with JSON data: $smsData")

                    // Write the JSON data to the request
                    val os = OutputStreamWriter(urlConnection.outputStream)
                    os.write(smsData.toString())
                    os.flush()
                    os.close()
                }

                val responseCode = urlConnection.responseCode
                Log.d(TAG, "Forwarding SMS to URL response code: $responseCode")
                if (responseCode == HttpURLConnection.HTTP_ACCEPTED ||
                    responseCode == HttpURLConnection.HTTP_OK ||
                    responseCode == HttpURLConnection.HTTP_CREATED) {
                    Log.d(TAG, "SMS forwarded successfully")

                    // Upload log data with status parameter set to true
                    updateForwards(context)
                    uploadLogToFirestore(context, logData, true,"","")
                }
                else {
                    Log.d(TAG, "Failed to forward SMS")

                    // Upload log data with status parameter set to false
                    uploadLogToFirestore(context,logData, false,"","")
                }

                urlConnection.disconnect()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error forwarding SMS to URL: $e")

        }
    }

    fun updateForwards(context: Context?, ) {
        val authHelper = FirebaseAuthHelper.getInstance()
        val firestore = authHelper.getAuthenticatedFirestore(context!!)
        val uid = authHelper.getCurrentUserId(context)

        if (uid != null && firestore != null) {
            val documentReference = firestore
                .collection("users")
                .document(uid)

            documentReference.get()
                .addOnSuccessListener { documentSnapshot ->
                    // Get the current value of the field
                    val noOfForwardsused: Int = (documentSnapshot["noOfForwardsused"] as? Long)?.toInt() ?: 0
                    val noOfForwardsPerMonth: Int = (documentSnapshot["noOfForwardsPerMonth"] as? Long)?.toInt() ?: 0

if(noOfForwardsused<noOfForwardsPerMonth) {
    // Update the field by incrementing its value
    documentReference
        .update("noOfForwardsused", noOfForwardsused + 1)
        .addOnSuccessListener {
            println("Document field updated successfully.")
        }
        .addOnFailureListener { e ->
            println("Error updating document field: $e")
        }
}
                }
                .addOnFailureListener { e ->
                    println("Error getting document: $e")
                }
        } else {
            Log.e(TAG, "Cannot update forwards: uid=$uid, firestore=${firestore != null}")
        }
    }
    private fun uploadLogToFirestore(context: Context?, logData: Map<String, Any>, status: Boolean, receiver: String,
                                     additionalMessage: String
    ) {
        val authHelper = FirebaseAuthHelper.getInstance()
        val firestore = authHelper.getAuthenticatedFirestore(context!!)
        val uid = authHelper.getCurrentUserId(context)

        if (uid != null && firestore != null) {
            val logCollection = firestore
                .collection("users")
                .document(uid)
                .collection("logs")

            val logEntry = logData.toMutableMap()
            logEntry["status"] = status // Set the status parameter
            logEntry["receiver"] = receiver // Set the receiver parameter
            logEntry["additionalMessage"] = additionalMessage

            logCollection.add(logEntry)
                .addOnSuccessListener {
                    Log.d(TAG, "Log data uploaded successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error uploading log data: ${e.message}")
                }
        } else {
            Log.e(TAG, "Cannot upload log: uid=$uid, firestore=${firestore != null}")
        }
    }





    companion object {
        // Constants for message handling
        private const val EMAIL_SENT_SUCCESS = 1
        private const val EMAIL_SENT_FAILURE = 2
        private const val TAG = "SmsBackgroundReceiver"

        // Singleton instance
        private var instance: SmsBackgroundReceiver? = null

        @Synchronized
        fun getInstance(): SmsBackgroundReceiver {
            if (instance == null) {
                instance = SmsBackgroundReceiver()
            }
            return instance!!
        }
    }



   private inner class SendEmailThread internal constructor(
     private val  context: Context?,
       private val  recipients: List<String>,
       private val subject: String,
       private val messageText: String,
       private val  logData: Map<String, Any>
   ) :
       Thread() {
       override fun run() {


               recipients?.forEach { recipient ->
                   try {



                       val emailSender = AmazonSESSMTPSample()
                       val recipient = recipient
                       val subject = subject
                       val message = messageText
                       emailSender.sendEmail(context, recipient, subject, message)

                       updateForwards(context)
                       uploadLogToFirestore(context,logData, true,"$recipient",
                           "")




                   } catch (ex: java.lang.Exception) {
                       uploadLogToFirestore(context,logData, false,"$recipient","")


                   }


               }
       }
   }


    // Call this method to send the email
    private fun sendEmailWithLogData(context: Context?, recipients: List<String>, subject: String, messageText: String, logData: Map<String, Any>) {
        val thread: SendEmailThread = SendEmailThread(context, recipients, subject, messageText, logData)
        thread.start()
    }





}

