import 'package:flutter/material.dart';
import 'package:flutter_signin_button/button_list.dart';
import 'package:flutter_signin_button/button_view.dart';
import 'package:get/get.dart';
import '../../controller/auth_controller.dart';
import '../../style.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final AuthController authController = Get.find();

  @override
  void initState() {
    super.initState();
    authController.checkForUpdate();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      // backgroundColor: const Color(0xff222222),
      body: SingleChildScrollView(
        child: SizedBox(
          height: Get.height,
          width: Get.width,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Spacer(),
              Center(
                  child: Image.asset(
                'assets/logo.png',
                height: Get.height * 0.15,
              )),
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  txt(
                    txt: 'SMS',
                    fontColor: blackishColor,
                    maxLines: 2,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.center,
                    fontSize: 26,
                  ),
                  txt(
                    txt: 'AutoForwarder',
                    fontColor: maincolor,
                    maxLines: 2,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                    fontSize: 26,
                  ),
                ],
              ),
              SizedBox(
                height: Get.height * 0.05,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  txt(txt: 'Log In', fontColor: blackishColor, fontSize: 30)
                ],
              ),
              SizedBox(
                height: Get.height * 0.05,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  txt(
                      txt:
                          'Please login to your account to start\nforwarding messages',
                      maxLines: 3,
                      textAlign: TextAlign.center,
                      fontColor: blackishColor,
                      fontSize: 22)
                ],
              ),
              SizedBox(
                height: Get.height * 0.05,
              ),
              Obx(
                () => SignInButton(
                  Buttons.GoogleDark,
                  onPressed: authController.isAuthUpdating.isTrue
                      ? () {}
                      : () {
                          authController.googleLogin();
                        },
                ),
              ),
              Obx(
                () => SignInButton(
                  Buttons.Facebook,
                  onPressed: authController.isAuthUpdating.isTrue
                      ? () {}
                      : () {
                          authController.signInWithFacebook();
                        },
                ),
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
