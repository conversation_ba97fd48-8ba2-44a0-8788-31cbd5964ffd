package com.smsautoforward.smsautoforwardapp

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await

class FirebaseAuthHelper private constructor() {
    
    companion object {
        private const val TAG = "FirebaseAuthHelper"
        private const val PREFS_NAME = "FIREBASE_AUTH_PREFS"
        private const val KEY_AUTH_TOKEN = "AUTH_TOKEN"
        private const val KEY_USER_ID = "USER_ID"
        
        @Volatile
        private var INSTANCE: FirebaseAuthHelper? = null
        
        fun getInstance(): FirebaseAuthHelper {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseAuthHelper().also { INSTANCE = it }
            }
        }
    }
    
    private val auth = FirebaseAuth.getInstance()
    
    /**
     * Store authentication token when user logs in from Flutter
     */
    fun storeAuthToken(context: Context, userId: String, authToken: String?) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().apply {
            putString(KEY_USER_ID, userId)
            putString(KEY_AUTH_TOKEN, authToken)
            apply()
        }
        Log.d(TAG, "Stored auth token for user: $userId")
    }
    
    /**
     * Get stored authentication token
     */
    private fun getStoredAuthToken(context: Context): String? {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(KEY_AUTH_TOKEN, null)
    }
    
    /**
     * Get stored user ID
     */
    private fun getStoredUserId(context: Context): String? {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(KEY_USER_ID, null)
    }
    
    /**
     * Check if user is authenticated and return Firestore instance
     */
    fun getAuthenticatedFirestore(context: Context): FirebaseFirestore? {
        return try {
            val currentUser = auth.currentUser
            if (currentUser != null) {
                Log.d(TAG, "User is authenticated: ${currentUser.uid}")
                FirebaseFirestore.getInstance()
            } else {
                // Try to get user ID from SharedPreferences (fallback)
                val storedUserId = getStoredUserId(context)
                if (storedUserId != null) {
                    Log.d(TAG, "Using stored user ID: $storedUserId")
                    // Note: This is a fallback - ideally we should have proper auth
                    FirebaseFirestore.getInstance()
                } else {
                    Log.e(TAG, "No authenticated user found")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting authenticated Firestore: ${e.message}")
            null
        }
    }
    
    /**
     * Get current user ID (from auth or SharedPreferences)
     */
    fun getCurrentUserId(context: Context): String? {
        return auth.currentUser?.uid ?: run {
            // Fallback to SharedPreferences
            val prefs = context.getSharedPreferences("UUIDSTRING", Context.MODE_PRIVATE)
            prefs.getString("UUIDSTRING", null)
        }
    }
    
    /**
     * Clear stored authentication data
     */
    fun clearAuthData(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
        
        // Also clear the UUID from the other SharedPreferences
        val uuidPrefs = context.getSharedPreferences("UUIDSTRING", Context.MODE_PRIVATE)
        uuidPrefs.edit().clear().apply()
        
        Log.d(TAG, "Cleared auth data")
    }
}
